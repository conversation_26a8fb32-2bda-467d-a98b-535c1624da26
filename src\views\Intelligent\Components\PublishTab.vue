<script setup lang="ts">
  // 定义 props
  interface Props {
    agentId: string;
  }
  const props = defineProps<Props>();

  // 定义 emits
  const emit = defineEmits<{
    'publish-request': [];
  }>();
</script>

<template>
  <div class="publish-tab-container">
    <div class="placeholder-content">
      <h3>发布管理</h3>
      <p>发布相关功能正在开发中...</p>
    </div>
  </div>
</template>

<style scoped>
.publish-tab-container {
  padding: 20px;
  height: 100%;
}

.placeholder-content {
  text-align: center;
  margin-top: 100px;
}
</style>
